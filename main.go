package main

import (
	"jwt-auth-app/config"
	"jwt-auth-app/handlers"
	"jwt-auth-app/middleware"
	"jwt-auth-app/models"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

func main() {
	// Initialize database
	config.InitDatabase()
	defer config.CloseDatabase()

	// Start cleanup routine for expired tokens
	go func() {
		ticker := time.NewTicker(24 * time.Hour) // Run cleanup daily
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				if err := models.CleanupExpiredTokens(); err != nil {
					log.Println("Error cleaning up expired tokens:", err)
				} else {
					log.Println("Expired tokens cleaned up successfully")
				}
			}
		}
	}()

	// Initialize Gin router
	r := gin.Default()

	// CORS middleware
	r.Use(func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "JWT Auth API is running",
			"time":    time.Now().Format(time.RFC3339),
		})
	})

	// Public routes
	auth := r.Group("/api/auth")
	{
		auth.POST("/signup", handlers.Signup)
		auth.POST("/login", handlers.Login)
	}

	// Protected routes
	protected := r.Group("/api")
	protected.Use(middleware.AuthMiddleware())
	{
		protected.POST("/logout", handlers.Logout)
		protected.GET("/profile", handlers.GetProfile)
	}

	// Example protected route
	protected.GET("/dashboard", func(c *gin.Context) {
		userID, _ := c.Get("user_id")
		email, _ := c.Get("email")
		
		c.JSON(200, gin.H{
			"message": "Welcome to your dashboard!",
			"user_id": userID,
			"email":   email,
		})
	})

	// Start server
	log.Println("Server starting on :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
