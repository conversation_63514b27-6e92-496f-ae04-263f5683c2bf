package models

import (
	"errors"
	"sync"
	"time"

	"golang.org/x/crypto/bcrypt"
)

type User struct {
	ID        uint      `json:"id"`
	Email     string    `json:"email"`
	Password  string    `json:"-"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type TokenBlacklist struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
}

// In-memory storage
var (
	users           = make(map[uint]*User)
	usersByEmail    = make(map[string]*User)
	blacklistedTokens = make(map[string]*TokenBlacklist)
	userIDCounter   uint = 1
	userMutex       sync.RWMutex
	tokenMutex      sync.RWMutex
)

// HashPassword hashes the user's password
func (u *User) HashPassword() error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.Password = string(hashedPassword)
	return nil
}

// CheckPassword verifies the password
func (u *User) CheckPassword(password string) error {
	return bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
}

// CreateUser creates a new user in memory
func CreateUser(email, password string) (*User, error) {
	userMutex.Lock()
	defer userMutex.Unlock()

	// Check if user already exists
	if _, exists := usersByEmail[email]; exists {
		return nil, errors.New("user with this email already exists")
	}

	user := &User{
		ID:        userIDCounter,
		Email:     email,
		Password:  password,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Hash the password
	if err := user.HashPassword(); err != nil {
		return nil, err
	}

	// Store user in memory
	users[user.ID] = user
	usersByEmail[email] = user
	userIDCounter++

	return user, nil
}

// GetUserByEmail retrieves a user by email
func GetUserByEmail(email string) (*User, error) {
	userMutex.RLock()
	defer userMutex.RUnlock()

	user, exists := usersByEmail[email]
	if !exists {
		return nil, errors.New("user not found")
	}
	return user, nil
}

// GetUserByID retrieves a user by ID
func GetUserByID(id uint) (*User, error) {
	userMutex.RLock()
	defer userMutex.RUnlock()

	user, exists := users[id]
	if !exists {
		return nil, errors.New("user not found")
	}
	return user, nil
}

// BlacklistToken adds a token to the blacklist
func BlacklistToken(token string, expiresAt time.Time) error {
	tokenMutex.Lock()
	defer tokenMutex.Unlock()

	blacklistedToken := &TokenBlacklist{
		Token:     token,
		ExpiresAt: expiresAt,
		CreatedAt: time.Now(),
	}
	blacklistedTokens[token] = blacklistedToken
	return nil
}

// IsTokenBlacklisted checks if a token is blacklisted
func IsTokenBlacklisted(token string) bool {
	tokenMutex.RLock()
	defer tokenMutex.RUnlock()

	blacklistedToken, exists := blacklistedTokens[token]
	if !exists {
		return false
	}

	// Check if token has expired
	if time.Now().After(blacklistedToken.ExpiresAt) {
		return false
	}

	return true
}

// CleanupExpiredTokens removes expired tokens from blacklist
func CleanupExpiredTokens() error {
	tokenMutex.Lock()
	defer tokenMutex.Unlock()

	now := time.Now()
	for token, blacklistedToken := range blacklistedTokens {
		if now.After(blacklistedToken.ExpiresAt) {
			delete(blacklistedTokens, token)
		}
	}
	return nil
}
