package config

import (
	"database/sql"
	"fmt"
	"jwt-auth-app/models"
	"log"
	"os"

	_ "github.com/tursodatabase/libsql-client-go/libsql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDatabase initializes the Turso database connection
func InitDatabase() {
	var err error

	// Get Turso database URL and token from environment variables
	tursoURL := os.Getenv("TURSO_DATABASE_URL")
	tursoToken := os.Getenv("TURSO_AUTH_TOKEN")

	// If environment variables are not set, use default values
	if tursoURL == "" {
		// You need to replace this with your actual Turso database URL
		// Format: libsql://your-database-name.turso.io
		log.Fatal("TURSO_DATABASE_URL environment variable is required. Please set it to your Turso database URL.")
	}
	if tursoToken == "" {
		tursoToken = "***************************************************************************************************************************************************************************************************************************************************************************************************************"
	}

	// Create connection string with auth token
	connectionString := fmt.Sprintf("%s?authToken=%s", tursoURL, tursoToken)

	// Open connection using libsql driver
	sqlDB, err := sql.Open("libsql", connectionString)
	if err != nil {
		log.Fatal("Failed to connect to Turso database:", err)
	}

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		log.Fatal("Failed to ping Turso database:", err)
	}

	// Initialize GORM with the existing connection
	models.DB, err = gorm.Open(sqlite.Dialector{
		Conn: sqlDB,
	}, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("Failed to initialize GORM with Turso:", err)
	}

	// Auto-migrate the schema
	err = models.DB.AutoMigrate(&models.User{}, &models.TokenBlacklist{})
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	log.Println("Turso database connected and migrated successfully")
}

// CloseDatabase closes the database connection
func CloseDatabase() {
	sqlDB, err := models.DB.DB()
	if err != nil {
		log.Println("Error getting database instance:", err)
		return
	}

	if err := sqlDB.Close(); err != nil {
		log.Println("Error closing database:", err)
	} else {
		log.Println("Database connection closed successfully")
	}
}
