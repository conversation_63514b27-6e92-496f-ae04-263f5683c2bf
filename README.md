# JWT Authentication API with Go

A secure JWT-based authentication system built with Go, Gin, GORM, and Turso (SQLite Cloud).

## Features

- User registration (signup)
- User authentication (login)
- Secure logout with token blacklisting
- JWT token-based authentication
- Password hashing with bcrypt
- Turso (SQLite Cloud) database with GORM
- CORS support
- Input validation
- Protected routes middleware
- Environment variable configuration

## API Endpoints

### Public Endpoints

#### Health Check
```
GET /health
```

#### User Registration
```
POST /api/auth/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### User Login
```
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Protected Endpoints (Require Authorization Header)

#### User Logout
```
POST /api/logout
Authorization: Bearer <jwt_token>
```

#### Get User Profile
```
GET /api/profile
Authorization: Bearer <jwt_token>
```

#### Dashboard (Example)
```
GET /api/dashboard
Authorization: Bearer <jwt_token>
```

## Getting Started

### Prerequisites

- Go 1.21 or higher
- Git
- Turso account and database

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd jwt-auth-app
```

2. Install dependencies:
```bash
go mod tidy
```

3. Set up Turso database:
   - Create a Turso account at https://turso.tech
   - Create a new database
   - Get your database URL and auth token

4. Configure environment variables:
   - Copy `.env.example` to `.env`
   - Update `TURSO_DATABASE_URL` with your Turso database URL
   - Update `TURSO_AUTH_TOKEN` with your Turso auth token

5. Run the application:
```bash
go run main.go
```

The server will start on `http://localhost:8080`

### Testing the API

You can test the API using curl or any HTTP client:

1. **Register a new user:**
```bash
curl -X POST http://localhost:8080/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

2. **Login:**
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

3. **Access protected route:**
```bash
curl -X GET http://localhost:8080/api/profile \
  -H "Authorization: Bearer <your_jwt_token>"
```

4. **Logout:**
```bash
curl -X POST http://localhost:8080/api/logout \
  -H "Authorization: Bearer <your_jwt_token>"
```

## Project Structure

```
├── main.go              # Application entry point
├── config/
│   └── database.go      # Database configuration
├── models/
│   └── user.go          # User model and database operations
├── handlers/
│   └── auth.go          # Authentication handlers
├── middleware/
│   └── auth.go          # JWT authentication middleware
├── utils/
│   └── jwt.go           # JWT utilities
├── go.mod               # Go module file
└── README.md            # This file
```

## Security Features

- **Password Hashing**: Uses bcrypt for secure password storage
- **JWT Tokens**: Stateless authentication with configurable expiration
- **Token Blacklisting**: Secure logout by blacklisting tokens
- **Input Validation**: Email format and password strength validation
- **CORS**: Cross-origin resource sharing support
- **Automatic Cleanup**: Expired blacklisted tokens are cleaned up daily

## Configuration

### JWT Secret
Change the JWT secret in `utils/jwt.go`:
```go
var jwtSecret = []byte("your-secret-key-change-this-in-production")
```

### Database
The application uses Turso (SQLite Cloud) for data persistence. Configure your Turso database URL and auth token in the `.env` file.

## Production Considerations

1. **Environment Variables**: Move sensitive configuration to environment variables
2. **HTTPS**: Use HTTPS in production
3. **Rate Limiting**: Implement rate limiting for authentication endpoints
4. **Logging**: Add comprehensive logging
5. **Database**: Consider using PostgreSQL or MySQL for production
6. **JWT Secret**: Use a strong, randomly generated secret key
7. **Token Expiration**: Configure appropriate token expiration times

## License

This project is licensed under the MIT License.
